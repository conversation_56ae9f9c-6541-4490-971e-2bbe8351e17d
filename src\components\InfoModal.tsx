import { FC, ReactNode } from 'react';
import { PzPopup } from './Shared/PzPopup';

interface InfoModalProps {
  isOpen: boolean;
  onClose: () => void;
  rules: ReactNode;
  title?: string;
}

const InfoModal: FC<InfoModalProps> = ({ isOpen, onClose, rules, title = 'Game Rules' }) => {
  if (!isOpen) return null;
  
  return (
    <PzPopup title={title} onClose={onClose}>
      <div className="text-white">
        {rules}
      </div>
    </PzPopup>
  );
};

export default InfoModal;